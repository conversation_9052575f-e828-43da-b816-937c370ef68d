#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全局剪贴板管理器
提供跨表格的统一剪贴板管理功能，解决不同表格间复制粘贴隔离的问题
"""

import logging
import csv
import io
import hashlib
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal


class GlobalClipboardManager(QObject):
    """
    全局剪贴板管理器（单例模式）
    统一管理所有表格的复制粘贴操作，确保跨表格数据共享
    """
    
    # 单例实例
    _instance = None
    _initialized = False
    
    # 信号
    clipboard_changed = pyqtSignal()  # 剪贴板内容变化信号
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(GlobalClipboardManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化全局剪贴板管理器"""
        if self._initialized:
            return
            
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # 剪贴板数据存储
        self.clipboard_data = []  # 内部剪贴板数据（二维数组）
        self.clipboard_source = None  # 数据来源标识
        self.clipboard_timestamp = None  # 数据时间戳
        self.is_cut_operation = False  # 标记是否为剪切操作
        self.cut_source_cells = []  # 存储剪切操作的源单元格位置信息
        self.cut_source_table = None  # 存储剪切操作的源表格引用
        self.last_system_clipboard_text = ""  # 上次检查的系统剪贴板文本内容
        self.system_clipboard_hash = None  # 系统剪贴板内容哈希值，用于快速比较
        self.test_mode = False  # 测试模式标志，用于模拟系统剪贴板
        
        # 标记为已初始化
        GlobalClipboardManager._initialized = True
        
        self.logger.info("全局剪贴板管理器已初始化")
    
    def copy_data(self, data, source_info=None, is_cut=False, cut_cells=None, cut_table=None):
        """
        复制数据到全局剪贴板

        参数:
            data: 二维数组数据 [[cell1, cell2], [cell3, cell4], ...]
            source_info: 数据来源信息（可选）
            is_cut: 是否为剪切操作（默认False）
            cut_cells: 剪切操作的源单元格位置列表（可选）
            cut_table: 剪切操作的源表格引用（可选）
        """
        if not data:
            self.logger.warning("尝试复制空数据")
            return

        # 存储数据
        self.clipboard_data = data
        self.clipboard_source = source_info
        self.clipboard_timestamp = time.time()  # 记录复制时间戳
        self.is_cut_operation = is_cut
        self.cut_source_cells = cut_cells if cut_cells else []
        self.cut_source_table = cut_table

        # 同时存储到系统剪贴板，以便跨应用粘贴
        try:
            text = self._data_to_text(data)
            clipboard = QApplication.clipboard()
            if clipboard:
                clipboard.setText(text)
                # 更新系统剪贴板跟踪信息
                self.last_system_clipboard_text = text
                self.system_clipboard_hash = self._calculate_text_hash(text)
        except Exception as e:
            self.logger.warning(f"无法访问系统剪贴板: {e}")

        # 发射信号通知剪贴板内容变化
        self.clipboard_changed.emit()

        operation_type = "剪切" if is_cut else "复制"
        self.logger.info(f"已{operation_type}数据到全局剪贴板: {len(data)}行 x {max(len(row) for row in data) if data else 0}列")
        if source_info:
            self.logger.info(f"数据来源: {source_info}")
        if is_cut and cut_cells:
            self.logger.info(f"剪切源单元格数量: {len(cut_cells)}")

    def cut_data(self, data, source_info=None, cut_cells=None, cut_table=None):
        """
        剪切数据到全局剪贴板（专门用于剪切操作的便捷方法）

        参数:
            data: 二维数组数据 [[cell1, cell2], [cell3, cell4], ...]
            source_info: 数据来源信息（可选）
            cut_cells: 剪切操作的源单元格位置列表（可选）
            cut_table: 剪切操作的源表格引用（可选）
        """
        self.copy_data(data, source_info, is_cut=True, cut_cells=cut_cells, cut_table=cut_table)

    def get_clipboard_data(self):
        """
        获取剪贴板数据

        返回:
            tuple: (data, source_info, is_cut, cut_cells, cut_table)
            其中data是二维数组，source_info是来源信息，is_cut是剪切标记，
            cut_cells是剪切源单元格位置，cut_table是剪切源表格
        """
        # 首先检查系统剪贴板是否有新内容
        if self._check_system_clipboard_changed():
            try:
                clipboard = QApplication.clipboard()
                if clipboard:
                    text = clipboard.text()
                    if text and text.strip():
                        # 系统剪贴板有新内容，更新内部缓存
                        self.clipboard_data = self._parse_clipboard_text(text)
                        self.clipboard_source = "系统剪贴板"
                        self.clipboard_timestamp = time.time()
                        # 系统剪贴板的内容不是剪切操作
                        self.is_cut_operation = False
                        self.cut_source_cells = []
                        self.cut_source_table = None
                        self.last_system_clipboard_text = text
                        self.system_clipboard_hash = self._calculate_text_hash(text)
                        self.logger.info("检测到系统剪贴板内容变化，已更新内部缓存")
            except Exception as e:
                self.logger.warning(f"无法访问系统剪贴板: {e}")

        # 如果内部剪贴板仍为空，尝试从系统剪贴板获取（兼容性处理）
        if not self.clipboard_data:
            try:
                clipboard = QApplication.clipboard()
                if clipboard:
                    text = clipboard.text()
                    if text and text.strip():
                        self.clipboard_data = self._parse_clipboard_text(text)
                        self.clipboard_source = "系统剪贴板"
                        self.clipboard_timestamp = time.time()
                        # 系统剪贴板的内容不是剪切操作
                        self.is_cut_operation = False
                        self.cut_source_cells = []
                        self.cut_source_table = None
                        self.last_system_clipboard_text = text
                        self.system_clipboard_hash = self._calculate_text_hash(text)
                        self.logger.info("从系统剪贴板获取数据")
            except Exception as e:
                self.logger.warning(f"无法访问系统剪贴板: {e}")

        return (self.clipboard_data, self.clipboard_source, self.is_cut_operation,
                self.cut_source_cells, self.cut_source_table)

    def clear_cut_state(self):
        """
        清除剪切状态，将剪切操作转换为普通复制操作
        通常在粘贴完成后调用
        """
        if self.is_cut_operation:
            self.is_cut_operation = False
            self.cut_source_cells = []
            self.cut_source_table = None
            self.logger.info("已清除剪切状态")

    def is_cut_data(self):
        """
        检查当前剪贴板数据是否为剪切操作

        返回:
            bool: True表示是剪切操作，False表示是复制操作
        """
        return self.is_cut_operation

    def has_data(self):
        """
        检查是否有剪贴板数据

        返回:
            bool: 是否有数据
        """
        # 首先检查系统剪贴板是否有新内容
        if self._check_system_clipboard_changed():
            return True

        # 检查内部缓存
        if self.clipboard_data:
            return True

        # 检查系统剪贴板
        try:
            clipboard = QApplication.clipboard()
            if clipboard:
                text = clipboard.text()
                return bool(text and text.strip())
        except Exception as e:
            self.logger.warning(f"无法访问系统剪贴板: {e}")

        return False
    
    def clear(self):
        """清空剪贴板数据"""
        self.clipboard_data = []
        self.clipboard_source = None
        self.clipboard_timestamp = None
        self.last_system_clipboard_text = ""
        self.system_clipboard_hash = None

        self.logger.info("已清空全局剪贴板")

    def set_test_mode(self, enabled=True):
        """
        设置测试模式

        参数:
            enabled: 是否启用测试模式
        """
        self.test_mode = enabled
        if enabled:
            self.logger.info("已启用测试模式")
        else:
            self.logger.info("已禁用测试模式")

    def simulate_system_clipboard(self, text):
        """
        模拟系统剪贴板内容（仅在测试模式下使用）

        参数:
            text: 模拟的剪贴板文本内容
        """
        if not self.test_mode:
            self.logger.warning("非测试模式下无法模拟系统剪贴板")
            return

        self.last_system_clipboard_text = text
        self.system_clipboard_hash = self._calculate_text_hash(text)
        self.logger.info(f"已模拟系统剪贴板内容: {len(text)}字符")

    def refresh_from_system_clipboard(self):
        """
        强制从系统剪贴板刷新数据
        用于确保获取最新的剪贴板内容

        返回:
            bool: 是否成功刷新
        """
        try:
            # 在测试模式下，使用模拟的系统剪贴板内容
            if self.test_mode and self.last_system_clipboard_text:
                text = self.last_system_clipboard_text
                self.logger.info("测试模式：使用模拟的系统剪贴板内容")
            else:
                # 正常模式：从真实系统剪贴板获取
                clipboard = QApplication.clipboard()
                if not clipboard:
                    # 如果无法访问系统剪贴板，但有模拟内容，则使用模拟内容
                    if self.last_system_clipboard_text:
                        text = self.last_system_clipboard_text
                        self.logger.info("无法访问系统剪贴板，使用模拟内容")
                    else:
                        return False
                else:
                    text = clipboard.text()

            if not text or not text.strip():
                return False

            # 计算新的哈希值
            new_hash = self._calculate_text_hash(text)

            # 无论是否变化，都更新内部缓存（强制刷新）
            self.clipboard_data = self._parse_clipboard_text(text)
            self.clipboard_source = "系统剪贴板（强制刷新）"
            self.clipboard_timestamp = time.time()
            self.last_system_clipboard_text = text
            self.system_clipboard_hash = new_hash

            self.logger.info("已强制从系统剪贴板刷新数据")
            return True

        except Exception as e:
            self.logger.warning(f"强制刷新系统剪贴板时出错: {e}")
            # 如果出错但有模拟内容，尝试使用模拟内容
            if self.last_system_clipboard_text:
                try:
                    self.clipboard_data = self._parse_clipboard_text(self.last_system_clipboard_text)
                    self.clipboard_source = "系统剪贴板（异常恢复）"
                    self.clipboard_timestamp = time.time()
                    self.logger.info("异常恢复：使用模拟剪贴板内容")
                    return True
                except:
                    pass
            return False

    def _check_system_clipboard_changed(self):
        """
        检查系统剪贴板内容是否发生变化

        返回:
            bool: 系统剪贴板内容是否有变化
        """
        try:
            clipboard = QApplication.clipboard()
            if not clipboard:
                return False

            current_text = clipboard.text()
            if not current_text:
                # 如果系统剪贴板为空，但我们有缓存，说明可能被清空了
                return bool(self.last_system_clipboard_text)

            # 使用改进的内容比较机制
            if self._is_content_different(current_text):
                self.logger.info(f"检测到系统剪贴板内容变化: 内容长度从 {len(self.last_system_clipboard_text)} 变为 {len(current_text)}")
                return True

            return False

        except Exception as e:
            self.logger.warning(f"检查系统剪贴板变化时出错: {e}")
            return False

    def _calculate_text_hash(self, text):
        """
        计算文本内容的哈希值

        参数:
            text: 要计算哈希的文本

        返回:
            str: 文本的MD5哈希值
        """
        if not text:
            return None

        # 使用MD5计算哈希值（用于快速比较，不需要加密安全性）
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _is_content_different(self, new_text):
        """
        比较新文本内容与当前缓存是否不同

        参数:
            new_text: 新的文本内容

        返回:
            bool: 内容是否不同
        """
        if not new_text and not self.last_system_clipboard_text:
            return False

        if not new_text or not self.last_system_clipboard_text:
            return True

        # 首先比较哈希值（快速比较）
        new_hash = self._calculate_text_hash(new_text)
        if new_hash != self.system_clipboard_hash:
            return True

        # 如果哈希值相同，再比较实际内容（防止哈希冲突）
        return new_text != self.last_system_clipboard_text

    def get_clipboard_info(self):
        """
        获取剪贴板详细信息（用于调试）

        返回:
            dict: 剪贴板信息
        """
        try:
            clipboard = QApplication.clipboard()
            current_text = clipboard.text() if clipboard else ""

            return {
                "has_internal_data": bool(self.clipboard_data),
                "internal_source": self.clipboard_source,
                "internal_timestamp": self.clipboard_timestamp,
                "system_clipboard_text_length": len(current_text) if current_text else 0,
                "system_clipboard_hash": self._calculate_text_hash(current_text) if current_text else None,
                "cached_system_hash": self.system_clipboard_hash,
                "content_changed": self._is_content_different(current_text)
            }
        except Exception as e:
            return {"error": str(e)}

    def _data_to_text(self, data):
        """
        将二维数组数据转换为文本格式
        
        参数:
            data: 二维数组数据
            
        返回:
            str: 制表符分隔的文本
        """
        if not data:
            return ""
        
        # 处理单元格内的换行符和制表符
        processed_data = []
        for row in data:
            processed_row = []
            for cell in row:
                cell_str = str(cell) if cell is not None else ""
                # 保留单元格内的换行符，但用特殊标记替换制表符以避免冲突
                cell_str = cell_str.replace('\t', '    ')  # 将制表符替换为4个空格
                processed_row.append(cell_str)
            processed_data.append(processed_row)
        
        # 使用制表符分隔列，换行符分隔行
        return "\n".join(["\t".join(row) for row in processed_data])
    
    def _parse_clipboard_text(self, text):
        """
        解析剪贴板文本，支持多种格式

        参数:
            text: 剪贴板文本内容

        返回:
            list: 解析后的二维数组数据
        """
        if not text or not text.strip():
            self.logger.debug("剪贴板文本为空")
            return []

        # 记录原始文本信息用于调试
        text_info = {
            'length': len(text),
            'lines': len(text.split('\n')),
            'has_tabs': '\t' in text,
            'tab_count': text.count('\t'),
            'has_newlines': '\n' in text
        }
        self.logger.debug(f"解析剪贴板文本: {text_info}")

        try:
            # 检测数据格式并解析
            result = self._detect_and_parse_format(text)

            # 记录解析结果
            if result:
                result_info = {
                    'rows': len(result),
                    'columns': len(result[0]) if result else 0,
                    'total_cells': sum(len(row) for row in result)
                }
                self.logger.info(f"剪贴板解析成功: {result_info}")
            else:
                self.logger.warning("剪贴板解析结果为空")

            return result

        except Exception as e:
            self.logger.error(f"解析剪贴板数据失败: {e}")
            import traceback
            self.logger.debug(f"解析错误详情: {traceback.format_exc()}")

            # 降级到最基本的解析方法
            try:
                self.logger.info("尝试降级解析方法")
                return self._fallback_parse(text)
            except Exception as fallback_error:
                self.logger.error(f"降级解析也失败: {fallback_error}")
                return []

    def _fallback_parse(self, text):
        """
        降级解析方法，用于处理复杂或异常的剪贴板数据

        参数:
            text: 剪贴板文本

        返回:
            list: 解析后的数据
        """
        lines = text.split('\n')

        # 如果包含制表符，尝试简单的制表符分隔
        if '\t' in text:
            data = []
            for line in lines:
                if line.strip():  # 跳过空行
                    cells = line.split('\t')
                    data.append(cells)
            return data
        else:
            # 否则按单列处理
            return [[line] for line in lines if line.strip()]
    
    def _detect_and_parse_format(self, text):
        """
        检测并解析数据格式

        参数:
            text: 剪贴板文本内容

        返回:
            list: 解析后的二维数组数据
        """
        # 清理文本，移除首尾空白
        text = text.strip()

        if not text:
            return []

        # 首先进行更全面的格式分析
        format_info = self._analyze_text_format(text)

        # 根据分析结果选择合适的解析方法
        if format_info['is_single_cell']:
            self.logger.info("检测到Excel单元格内容")
            return [[text]]  # 作为单个单元格处理

        elif format_info['has_tabs'] and format_info['is_tabular']:
            self.logger.info(f"检测到Excel制表符分隔格式数据: {format_info['expected_columns']}列")
            return self._parse_tab_separated(text)

        elif format_info['is_csv']:
            self.logger.info("检测到CSV格式数据")
            return self._parse_csv_format(text)

        elif format_info['has_spaces'] and format_info['space_count'] >= 2:
            self.logger.info("检测到空格分隔格式数据")
            return self._parse_space_separated(text)

        else:
            # 默认处理：如果有制表符就按制表符处理，否则按单列处理
            if format_info['has_tabs']:
                self.logger.info("使用制表符格式解析")
                return self._parse_tab_separated(text)
            else:
                self.logger.info("使用单列格式解析")
                lines = text.split('\n')
                # 修复：确保单列多行数据保持正确的行列结构
                return [[line.strip()] for line in lines if line.strip()]

    def _analyze_text_format(self, text):
        """
        分析文本格式，返回详细的格式信息

        参数:
            text: 要分析的文本

        返回:
            dict: 格式分析结果
        """
        lines = text.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]

        if not non_empty_lines:
            return {'is_single_cell': False, 'has_tabs': False, 'is_tabular': False, 'is_csv': False, 'has_spaces': False, 'space_count': 0}

        # 分析制表符
        tab_counts = [line.count('\t') for line in non_empty_lines]
        has_tabs = any(count > 0 for count in tab_counts)

        # 判断是否为表格格式（制表符数量相对一致）
        is_tabular = False
        expected_columns = 1
        if has_tabs:
            from collections import Counter
            tab_counter = Counter(tab_counts)
            most_common_tabs = tab_counter.most_common(1)[0][0]
            most_common_count = tab_counter.most_common(1)[0][1]

            # 如果大部分行的制表符数量一致，认为是表格格式
            if most_common_count >= len(non_empty_lines) * 0.6:  # 60%以上的行制表符数量一致
                is_tabular = True
                expected_columns = most_common_tabs + 1

        # 分析CSV格式
        first_line = non_empty_lines[0]
        is_csv = self._is_csv_format(first_line)

        # 分析空格分隔
        space_counts = [line.count(' ') for line in non_empty_lines]
        has_spaces = any(count > 0 for count in space_counts)
        avg_space_count = sum(space_counts) / len(space_counts) if space_counts else 0

        # 判断是否为单个单元格内容
        is_single_cell = self._is_excel_single_cell(text)

        return {
            'is_single_cell': is_single_cell,
            'has_tabs': has_tabs,
            'is_tabular': is_tabular,
            'expected_columns': expected_columns,
            'is_csv': is_csv,
            'has_spaces': has_spaces,
            'space_count': avg_space_count,
            'line_count': len(non_empty_lines),
            'tab_counts': tab_counts
        }
    
    def _is_excel_single_cell(self, text):
        """
        检测是否为Excel单元格内容（包含换行但应该作为单个单元格处理）

        参数:
            text: 文本内容

        返回:
            bool: 是否为单元格内容
        """
        # 如果文本为空或只有一行，不需要特殊处理
        if not text or '\n' not in text:
            return False

        lines = text.split('\n')

        # 改进的启发式规则：
        # 1. 如果包含制表符，很可能是多单元格表格数据，不是单个单元格
        has_tabs = any('\t' in line for line in lines)
        if has_tabs:
            # 进一步检查：如果每行的制表符数量一致，可能是表格数据
            tab_counts = [line.count('\t') for line in lines if line.strip()]
            if tab_counts and len(set(tab_counts)) == 1 and tab_counts[0] > 0:
                # 制表符数量一致且大于0，很可能是表格数据
                return False
            # 如果制表符数量不一致，可能是单元格内容包含制表符

        # 2. 检查行数：单元格内容通常不会有太多行
        if len(lines) > 20:  # 增加阈值，避免误判
            return False

        # 3. 检查内容特征：
        # - 如果每行都很短且没有明显的表格结构，可能是单元格内容
        # - 如果有明显的表格特征（如多个制表符分隔的列），则不是单元格内容
        non_empty_lines = [line for line in lines if line.strip()]
        if not non_empty_lines:
            return False

        # 计算平均行长度
        avg_line_length = sum(len(line) for line in non_empty_lines) / len(non_empty_lines)

        # 4. 综合判断：
        # - 没有制表符或制表符不规律
        # - 行数适中（1-20行）
        # - 平均行长度不太长（避免误判大段文本）
        # 修复：排除明显的单列多行数据（如样车编号列表）
        if not has_tabs and len(non_empty_lines) <= 20 and avg_line_length < 200:
            # 检查是否为单列多行数据：每行内容相似且简短
            if len(non_empty_lines) > 1:
                # 如果多行内容都很短且相似（可能是ID、编号等），应该作为多行处理
                all_short_lines = all(len(line.strip()) < 50 for line in non_empty_lines)
                if all_short_lines:
                    # 进一步检查：如果所有行都是数字或简短的标识符，很可能是单列多行数据
                    all_simple_content = all(
                        line.strip().isdigit() or
                        (len(line.strip()) < 20 and not ' ' in line.strip())
                        for line in non_empty_lines
                    )
                    if all_simple_content:
                        return False  # 不是单个单元格，应该作为多行处理
            return True

        # 5. 特殊情况：即使有制表符，如果只有少数几行且制表符不规律，也可能是单元格内容
        # 但是要排除明显的多单元格Excel数据
        if has_tabs and len(non_empty_lines) <= 3:
            tab_counts = [line.count('\t') for line in non_empty_lines]
            # 如果制表符数量不一致，需要进一步分析
            if len(set(tab_counts)) > 1:
                # 检查是否符合Excel多单元格的特征：
                # - 有些行制表符数量为0（单元格内换行）
                # - 有些行制表符数量>0（完整行）
                zero_tab_lines = [count for count in tab_counts if count == 0]
                non_zero_tab_lines = [count for count in tab_counts if count > 0]

                # 如果有0制表符的行和非0制表符的行，很可能是Excel多单元格数据
                if zero_tab_lines and non_zero_tab_lines:
                    # 进一步检查：非0制表符的行是否有一致的制表符数量
                    if len(set(non_zero_tab_lines)) == 1 and non_zero_tab_lines[0] >= 2:
                        # 这很可能是Excel多单元格数据，不是单个单元格
                        return False

                # 如果制表符数量很少，可能是单元格内容
                if max(tab_counts) <= 1:
                    return True

        return False
    
    def _is_csv_format(self, line):
        """检测是否为CSV格式"""
        return ',' in line and ('"' in line or line.count(',') >= 1)
    
    def _parse_csv_format(self, text):
        """解析CSV格式数据"""
        try:
            csv_reader = csv.reader(io.StringIO(text))
            data = []
            for row in csv_reader:
                cleaned_row = [cell.strip() for cell in row]
                data.append(cleaned_row)
            return data
        except Exception as e:
            self.logger.warning(f"CSV解析失败，降级到逗号分隔解析: {e}")
            lines = text.split('\n')
            return [line.split(',') for line in lines if line.strip()]
    
    def _parse_tab_separated(self, text):
        """
        解析制表符分隔格式数据
        正确处理Excel中单元格内的换行符
        """
        if not text:
            return []

        # 使用改进的解析算法，能够正确处理单元格内的换行符
        return self._parse_excel_tab_format(text)

    def _parse_excel_tab_format(self, text):
        """
        解析Excel制表符分隔格式，正确处理单元格内换行符

        Excel复制多单元格时的格式特点：
        1. 列之间用制表符(\t)分隔
        2. 行之间用换行符(\n)分隔
        3. 单元格内的换行符也是\n，需要特殊处理

        算法思路：
        1. 分析文本结构，找出可能的列数
        2. 逐行解析，根据制表符数量判断是新行还是单元格内容的续行

        参数:
            text: Excel复制的制表符分隔文本

        返回:
            list: 解析后的二维数组数据
        """
        if not text:
            return []

        # 使用更简单但更可靠的算法
        return self._parse_tab_with_multiline_cells(text)

    def _parse_tab_with_multiline_cells(self, text):
        """
        解析包含多行单元格的制表符分隔数据

        使用更精确的算法来处理Excel复制的数据

        参数:
            text: 制表符分隔的文本

        返回:
            list: 解析后的二维数组
        """
        if not text:
            return []

        # 使用基于状态机的解析方法
        return self._parse_excel_tsv_format(text)

    def _parse_excel_tsv_format(self, text):
        """
        使用改进的算法解析Excel TSV格式，正确处理包含换行符的多单元格数据

        核心思路：
        1. 智能识别真正的行边界，区分单元格内换行符和行分隔符
        2. 基于制表符分布和内容特征进行精确解析
        3. 正确重构包含换行符的单元格内容

        参数:
            text: TSV格式文本

        返回:
            list: 解析后的二维数组
        """
        if not text:
            return []

        lines = text.split('\n')
        if not lines:
            return []

        # 使用新的智能解析算法
        return self._smart_parse_excel_tsv(lines)

    def _smart_parse_excel_tsv(self, lines):
        """
        智能解析Excel TSV格式，正确处理包含换行符的单元格

        算法步骤：
        1. 分析制表符分布，识别标准行格式
        2. 使用前瞻和回溯策略识别真正的行边界
        3. 重构包含换行符的单元格内容

        参数:
            lines: 按换行符分割的行列表

        返回:
            list: 解析后的二维数组
        """
        # 过滤空行
        non_empty_lines = [(i, line) for i, line in enumerate(lines) if line.strip()]
        if not non_empty_lines:
            return []

        # 分析制表符分布
        tab_analysis = self._analyze_tab_distribution([line for _, line in non_empty_lines])

        if tab_analysis['is_single_column']:
            # 单列数据，每行独立处理
            return [[line.strip()] for _, line in non_empty_lines]

        # 多列数据，使用智能行识别算法
        return self._reconstruct_multicolumn_rows(non_empty_lines, tab_analysis)

    def _analyze_tab_distribution(self, lines):
        """
        分析制表符分布，确定数据结构特征

        参数:
            lines: 行列表

        返回:
            dict: 分析结果
        """
        from collections import Counter

        tab_counts = [line.count('\t') for line in lines]
        tab_counter = Counter(tab_counts)

        # 找出最常见的制表符数量
        most_common_tabs = tab_counter.most_common()

        if not most_common_tabs:
            return {
                'is_single_column': True,
                'standard_tab_count': 0,
                'expected_columns': 1,
                'confidence': 1.0
            }

        standard_tab_count = most_common_tabs[0][0]
        standard_count_frequency = most_common_tabs[0][1]
        total_lines = len(lines)

        # 计算置信度
        confidence = standard_count_frequency / total_lines

        # 判断是否为单列数据
        is_single_column = (standard_tab_count == 0 and confidence > 0.8)

        return {
            'is_single_column': is_single_column,
            'standard_tab_count': standard_tab_count,
            'expected_columns': standard_tab_count + 1,
            'confidence': confidence,
            'tab_distribution': dict(tab_counter),
            'total_lines': total_lines
        }

    def _reconstruct_multicolumn_rows(self, indexed_lines, tab_analysis):
        """
        重构多列数据行，正确处理包含换行符的单元格

        参数:
            indexed_lines: 带索引的行列表 [(index, line), ...]
            tab_analysis: 制表符分析结果

        返回:
            list: 重构后的二维数组
        """
        standard_tab_count = tab_analysis['standard_tab_count']
        result = []
        i = 0

        while i < len(indexed_lines):
            current_idx, current_line = indexed_lines[i]
            current_tab_count = current_line.count('\t')

            if current_tab_count == standard_tab_count:
                # 这是一个标准的完整行
                cells = current_line.split('\t')
                result.append(cells)
                i += 1
            elif current_tab_count < standard_tab_count:
                # 这可能是一个不完整的行，需要与后续行合并
                merged_row = self._merge_incomplete_row(indexed_lines, i, standard_tab_count)
                if merged_row:
                    result.append(merged_row['cells'])
                    i = merged_row['next_index']
                else:
                    # 无法合并，作为单独行处理
                    cells = current_line.split('\t')
                    # 补齐列数
                    while len(cells) < tab_analysis['expected_columns']:
                        cells.append("")
                    result.append(cells)
                    i += 1
            else:
                # 制表符数量超过标准，可能是数据错误或特殊情况
                cells = current_line.split('\t')
                result.append(cells)
                i += 1

        # 统一列数
        if result:
            max_columns = max(len(row) for row in result)
            for row in result:
                while len(row) < max_columns:
                    row.append("")

        self.logger.info(f"智能Excel TSV解析完成: {len(result)}行 x {max_columns if result else 0}列")
        return result

    def _merge_incomplete_row(self, indexed_lines, start_idx, standard_tab_count):
        """
        合并不完整的行，重构包含换行符的单元格

        参数:
            indexed_lines: 带索引的行列表
            start_idx: 起始行索引
            standard_tab_count: 标准制表符数量

        返回:
            dict: {'cells': [...], 'next_index': int} 或 None
        """
        if start_idx >= len(indexed_lines):
            return None

        # 收集需要合并的行
        lines_to_merge = []
        current_idx = start_idx
        total_tab_count = 0

        while current_idx < len(indexed_lines):
            _, line = indexed_lines[current_idx]
            tab_count = line.count('\t')

            lines_to_merge.append(line)
            total_tab_count += tab_count
            current_idx += 1

            # 如果累计制表符数量达到或超过标准数量，停止合并
            if total_tab_count >= standard_tab_count:
                break

            # 防止过度合并：最多合并5行
            if len(lines_to_merge) >= 5:
                break

        # 重构单元格内容
        if total_tab_count >= standard_tab_count:
            merged_text = '\n'.join(lines_to_merge)
            cells = self._reconstruct_cells_from_merged_text(merged_text, standard_tab_count)
            return {
                'cells': cells,
                'next_index': current_idx
            }

        return None

    def _reconstruct_cells_from_merged_text(self, merged_text, expected_tab_count):
        """
        从合并的文本中重构单元格内容

        参数:
            merged_text: 合并后的文本
            expected_tab_count: 期望的制表符数量

        返回:
            list: 重构后的单元格列表
        """
        # 按制表符分割，但要保留单元格内的换行符
        parts = merged_text.split('\t')

        # 如果分割后的部分数量正确，直接返回
        if len(parts) == expected_tab_count + 1:
            return parts

        # 如果部分数量不正确，尝试智能重构
        if len(parts) > expected_tab_count + 1:
            # 部分过多，可能是单元格内包含制表符，需要合并
            cells = []
            current_cell = ""
            tab_count = 0

            for part in parts:
                if tab_count < expected_tab_count:
                    if current_cell:
                        current_cell += '\t' + part
                    else:
                        current_cell = part
                    tab_count += 1

                    # 检查是否应该结束当前单元格
                    if tab_count == expected_tab_count or self._should_end_cell(current_cell, part):
                        cells.append(current_cell)
                        current_cell = ""
                else:
                    # 最后一个单元格
                    if current_cell:
                        current_cell += '\t' + part
                    else:
                        current_cell = part

            if current_cell:
                cells.append(current_cell)

            return cells
        else:
            # 部分不足，补齐空单元格
            cells = list(parts)
            while len(cells) < expected_tab_count + 1:
                cells.append("")
            return cells

    def _should_end_cell(self, current_cell, latest_part):
        """
        判断是否应该结束当前单元格

        这是一个启发式方法，基于内容特征判断
        """
        # 如果当前单元格已经很长，可能应该结束
        if len(current_cell) > 200:
            return True

        # 如果最新部分看起来像是新的单元格开始（短且简单）
        if len(latest_part.strip()) < 20 and not '\n' in latest_part:
            return True

        return False

    def _merge_lines_to_row(self, lines, expected_columns):
        """
        将多行内容合并为一个表格行

        参数:
            lines: 要合并的行列表
            expected_columns: 预期的列数

        返回:
            list: 合并后的行数据
        """
        if not lines:
            return []

        # 过滤空行
        non_empty_lines = [line for line in lines if line.strip()]
        if not non_empty_lines:
            return []

        # 检查是否为单列数据（没有制表符或制表符数量不一致）
        tab_counts = [line.count('\t') for line in non_empty_lines]

        # 如果所有行都没有制表符，且预期列数为1，这是单列多行数据
        if all(count == 0 for count in tab_counts) and expected_columns == 1:
            # 对于单列多行数据，每行应该作为单独的行处理，而不是合并
            # 这里返回第一行作为当前行，其他行需要在上层逻辑中单独处理
            return [non_empty_lines[0]]

        # 将所有行用换行符连接
        combined_text = '\n'.join(non_empty_lines)

        # 按制表符分割
        cells = combined_text.split('\t')

        # 确保列数正确
        while len(cells) < expected_columns:
            cells.append("")

        return cells[:expected_columns]

    def _reconstruct_row_from_parts(self, parts, expected_columns):
        """
        从多个部分重构一个完整的表格行

        参数:
            parts: 行的各个部分
            expected_columns: 预期的列数

        返回:
            list: 重构后的行数据
        """
        if not parts:
            return []

        # 将所有部分用换行符连接
        combined_text = '\n'.join(parts)

        # 按制表符分割
        cells = combined_text.split('\t')

        # 确保列数正确
        while len(cells) < expected_columns:
            cells.append("")

        return cells[:expected_columns]  # 截断多余的列



    def _parse_space_separated(self, text):
        """解析空格分隔格式数据"""
        lines = text.split('\n')
        data = []
        for line in lines:
            if line.strip():  # 跳过空行
                cells = [cell for cell in line.split(' ') if cell.strip()]
                data.append(cells)
        return data


# 全局实例
clipboard_manager = GlobalClipboardManager()
