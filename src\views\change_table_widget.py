#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零部件换装表UI组件
"""

import os
import json
import logging
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QComboBox, QLineEdit, QTextEdit, QDateEdit, QPushButton,
    QToolBar, QAction, QMenu, QMessageBox, QCheckBox, QFrame, QAbstractItemView,
    QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QIcon

# 公式功能相关导入
from src.views.base_table_widget import BaseTableWidget
from src.formula.formula_config_manager import FormulaConfigManager
import pandas as pd

from src.models.change import Change

class ChangeTableWidget(BaseTableWidget):
    """
    零部件换装表组件

    继承自BaseTableWidget，自动获得列公式计算功能：
    1. 列公式设置和管理
    2. 自动计算和更新
    3. 公式配置保存和加载
    4. 统一的用户交互界面
    """

    def __init__(self, parent=None):
        # 先调用父类初始化，传入表格名称
        super().__init__(table_name="零部件换装表", parent=parent)

        # 启用公式功能，支持用户手动添加公式列
        self.formula_enabled = True

        # 初始化数据模型
        self.change_model = Change()
        self.data_model = self.change_model  # 设置基类的data_model

        # 初始化字段配置
        self.field_groups = self._load_field_groups()

        # 加载字段定义配置
        self.field_definitions = self._load_field_definitions()

        # 加载显示设置
        self.display_settings = self._load_display_settings()

        # 初始化表格状态管理器
        from src.utils.table_state_manager import TableStateManager
        self.table_state_manager = TableStateManager('change_table')

        # 加载数据
        self.load_data()
    



    def _create_cell_widget(self, field_name, value, row=None, col=None):
        """创建单元格控件"""
        # 根据字段定义配置创建不同的控件
        field_def = self.field_definitions.get(field_name, {})
        field_type = field_def.get('type', 'QLineEdit')
        field_options = field_def.get('options', [])

        # 根据字段类型创建控件
        if field_type == 'QComboBox' and field_options:

            # 🔧 统一QComboBox样式：参考试验问题表的标准样式
            # 直接创建QComboBox，不使用容器，避免布局异常
            combo = QComboBox()
            valid_options = [opt for opt in field_options if opt and str(opt).strip()]
            # 🔧 关键修复：添加空选项，与其他表格保持一致，允许用户清空选择
            combo.addItem("")  # 添加空选项作为第一项
            combo.addItems(valid_options)
            combo.setEditable(True)  # 🔧 关键修复：设置为可编辑，支持复制粘贴功能
            combo.setInsertPolicy(QComboBox.NoInsert)  # 🔧 关键修复：禁止插入新项，保持下拉选项不变

            # 🔧 关键修复：设置QComboBox的大小策略和约束，防止布局异常
            # 使用Expanding策略允许控件在水平方向上扩展和收缩，但保持在单元格内
            combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            # 🔧 设置最小和最大宽度，确保控件不会过度缩小或扩展
            combo.setMinimumWidth(80)  # 增加最小宽度，确保内容可见
            combo.setMaximumWidth(300)  # 设置最大宽度，防止过度扩展

            # 🔧 设置高度自适应，与普通文本字段保持一致
            # 不设置固定高度或最大高度限制，让QComboBox完全自适应表格行高
            combo.setMinimumHeight(20)  # 设置较小的最小高度，确保基本可见性
            # 移除最大高度限制，让QComboBox能够随行高自适应调节
            combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 垂直方向也使用Expanding策略

            # 🔧 设置统一样式，确保在小宽度下也能正常显示，移除外层容器框
            combo.setStyleSheet("""
                QComboBox {
                    border: 1px solid #ccc;
                    border-radius: 3px;
                    padding: 2px 5px;
                    background-color: white;
                    min-width: 80px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    width: 12px;
                    height: 12px;
                }
            """)

            # 设置当前值
            if value:
                combo.setCurrentText(str(value))
            else:
                combo.setCurrentIndex(-1)  # 🔧 关键修复：空值时设置为无选择状态，允许用户清空

            # 🔧 关键修复：连接QComboBox的currentTextChanged信号
            # 这是修复QComboBox数据无法保存到数据库的核心代码
            if row is not None and col is not None:
                combo.currentTextChanged.connect(
                    lambda text, r=row, c=col: self._on_combo_text_changed(r, c, text))

            return combo

        elif field_type == 'QTextEdit':
            from PyQt5.QtWidgets import QTextEdit
            text_edit = QTextEdit()
            text_edit.setPlainText(str(value) if value else "")
            text_edit.setMaximumHeight(60)
            return text_edit

        elif field_type == 'QDateEdit':
            from PyQt5.QtWidgets import QDateEdit
            from PyQt5.QtCore import QDate
            date_edit = QDateEdit()
            if value:
                try:
                    date = QDate.fromString(str(value), "yyyy-MM-dd")
                    if date.isValid():
                        date_edit.setDate(date)
                except:
                    pass
            return date_edit

        else:
            # 默认使用QTableWidgetItem (QLineEdit类型)
            return None  # 返回None表示使用默认的文本单元格

    def _init_selection_handlers(self):
        """初始化选择处理器"""
        try:
            # 导入表格选择处理器以支持多选、复制、粘贴和删除功能
            from src.utils.table_selection_handler import TableSelectionHandler
            self.table_selection_handler = TableSelectionHandler(self.table)

            # 导入多单元格选择管理器以增强Ctrl和Shift多选功能
            from src.utils.multi_cell_selection_manager import MultiCellSelectionManager
            self.multi_cell_selection_manager = MultiCellSelectionManager(self.table)

            # 导入行插入管理器以支持行插入功能
            from src.utils.row_insert_manager import RowInsertManager
            self.row_insert_manager = RowInsertManager(self.table, self)

            # 连接多选信号
            self._connect_multi_selection_signals()

            self.logger.info("零部件换装表选择处理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化零部件换装表选择处理器失败: {e}")

    def _connect_multi_selection_signals(self):
        """连接多选相关信号"""
        try:
            if hasattr(self, 'multi_cell_selection_manager'):
                self.multi_cell_selection_manager.selection_changed.connect(self._on_multi_selection_changed)
                self.multi_cell_selection_manager.ctrl_selection.connect(self._on_ctrl_selection)
                self.multi_cell_selection_manager.shift_selection.connect(self._on_shift_selection)
                self.logger.info("多选信号连接成功")
        except Exception as e:
            self.logger.error(f"连接多选信号失败: {e}")

    def _on_multi_selection_changed(self, selected_cells):
        """处理多选变化"""
        self.logger.info(f"多选变化: 选中 {len(selected_cells)} 个单元格")

    def _on_ctrl_selection(self, row, col, all_selected):
        """处理Ctrl多选"""
        self.logger.info(f"Ctrl选择: 单元格({row}, {col}), 总选中: {len(all_selected)}")

    def _on_shift_selection(self, start_row, start_col, end_row, end_col, selected_range):
        """处理Shift范围选择"""
        self.logger.info(f"Shift选择: 从({start_row}, {start_col})到({end_row}, {end_col}), 选中: {len(selected_range)}")

    def _expand_json_fields(self, df):
        """展开JSON字段"""
        expanded_df = df.copy()

        try:
            # 首先确保所有固定字段和可变字段的列都存在
            # 从配置文件获取所有可能的字段
            all_fixed_fields = self.field_groups.get('固定字段', [])
            all_variable_fields = self.field_groups.get('可变字段', [])

            # 为所有固定字段和可变字段创建空列
            for field in all_fixed_fields + all_variable_fields:
                if field not in expanded_df.columns:
                    expanded_df[field] = None

            # 展开固定字段JSON
            if '零部件换装表固定字段' in df.columns:
                for idx, row in df.iterrows():
                    fixed_json = row['零部件换装表固定字段']
                    if fixed_json and fixed_json.strip():
                        try:
                            fixed_data = json.loads(fixed_json)
                            if isinstance(fixed_data, dict):
                                for key, value in fixed_data.items():
                                    if key in all_fixed_fields:  # 只处理配置中定义的字段
                                        expanded_df.at[idx, key] = value
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"解析固定字段JSON失败 (行{idx}): {e}")

            # 展开可变字段JSON
            if '零部件换装表可变字段' in df.columns:
                for idx, row in df.iterrows():
                    variable_json = row['零部件换装表可变字段']
                    if variable_json and variable_json.strip():
                        try:
                            variable_data = json.loads(variable_json)
                            if isinstance(variable_data, dict):
                                for key, value in variable_data.items():
                                    if key in all_variable_fields:  # 只处理配置中定义的字段
                                        expanded_df.at[idx, key] = value
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"解析可变字段JSON失败 (行{idx}): {e}")

            # 移除原始JSON列
            columns_to_drop = ['零部件换装表固定字段', '零部件换装表可变字段']
            expanded_df = expanded_df.drop(columns=[col for col in columns_to_drop if col in expanded_df.columns])

            self.logger.info(f"JSON字段展开完成，固定字段: {all_fixed_fields}, 可变字段: {all_variable_fields}")

        except Exception as e:
            self.logger.error(f"展开JSON字段失败: {e}")

        return expanded_df
    
    def add_record(self, batch_mode=False):
        """
        添加新记录

        参数:
            batch_mode: 是否为批量模式，批量模式下不会立即重新加载表格
        """
        try:
            # 创建默认数据
            default_data = {
                "样车编号": "",
                "换装零部件": "",
                "换装原因及主要考察点": "",
                "换装后情况": "",
                "换装时间": ""
            }

            # 添加记录
            record_id = self.change_model.add_change_record(default_data, field_groups=self.field_groups)

            if record_id:
                # 性能优化：批量模式下不重新加载表格，由调用方统一处理
                if not batch_mode:
                    self.load_data()

                self.record_added.emit(record_id)

                # 减少日志输出频率
                if record_id % 10 == 0:
                    self.logger.info(f"添加零部件换装记录成功，当前ID: {record_id}")

                return record_id
            else:
                if not batch_mode:
                    QMessageBox.warning(self, "警告", "添加记录失败")
                return None

        except Exception as e:
            self.logger.error(f"添加零部件换装记录失败: {e}")
            if not batch_mode:
                QMessageBox.critical(self, "错误", f"添加记录失败: {e}")
            return None

    def add_multiple_records(self, count):
        """
        批量添加多条记录

        参数:
            count: 要添加的记录数量

        返回:
            成功添加的记录ID列表
        """
        try:
            self.logger.info(f"开始批量添加 {count} 条零部件换装记录")

            # 临时禁用表格信号，避免在批量操作期间触发单元格更新事件
            original_signals_blocked = self.table.signalsBlocked()
            self.table.blockSignals(True)

            # 设置批量操作标志
            self._updating_display = True

            added_record_ids = []

            try:
                # 批量添加记录
                for i in range(count):
                    record_id = self.add_record(batch_mode=True)
                    if record_id:
                        added_record_ids.append(record_id)
                    else:
                        self.logger.warning(f"批量添加第 {i+1} 条零部件换装记录失败")

                # 批量操作完成后，统一重新加载表格
                if added_record_ids:
                    self.load_data()
                    self.logger.info(f"批量添加完成，成功添加 {len(added_record_ids)} 条零部件换装记录")

                    # 发射批量添加完成信号
                    for record_id in added_record_ids:
                        self.record_added.emit(record_id)

                return added_record_ids

            finally:
                # 恢复表格信号和标志
                self._updating_display = False
                self.table.blockSignals(original_signals_blocked)

        except Exception as e:
            self.logger.error(f"批量添加零部件换装记录失败: {e}")
            # 确保在异常情况下也恢复信号状态
            self._updating_display = False
            self.table.blockSignals(original_signals_blocked)
            QMessageBox.critical(self, "错误", f"批量添加记录失败: {e}")
            return []

    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            selected_rows = set()
            for item in self.table.selectedItems():
                selected_rows.add(item.row())
            
            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要删除的记录")
                return
            
            reply = QMessageBox.question(
                self, 
                "确认删除", 
                f"确定要删除选中的 {len(selected_rows)} 条记录吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success_count = 0
                for row in selected_rows:
                    # 获取记录ID（假设ID在第一列）
                    id_item = self.table.item(row, 0)
                    if id_item:
                        record_id = int(id_item.text())
                        if self.change_model.delete_change_record(record_id):
                            success_count += 1
                            self.record_deleted.emit(record_id)

                self.load_data()  # 重新加载数据
                QMessageBox.information(self, "成功", f"成功删除 {success_count} 条记录")

        except Exception as e:
            self.logger.error(f"删除零部件换装记录失败: {e}")
            QMessageBox.critical(self, "错误", f"删除记录失败: {e}")

    def _show_context_menu(self, position):
        """显示增强的右键菜单，与试验问题表保持一致"""
        menu = QMenu(self)

        # 创建 "新增" 的级联菜单
        add_menu = menu.addMenu("新增")
        add_blank_row_action = add_menu.addAction("新增空白行")

        # 添加行插入功能
        insert_above_action = menu.addAction("向上方插入行")
        insert_below_action = menu.addAction("向下方插入行")

        menu.addSeparator()

        # 复制、剪切、粘贴、删除操作
        copy_action = menu.addAction("复制")
        cut_action = menu.addAction("剪切")
        paste_action = menu.addAction("粘贴")
        paste_as_new_action = menu.addAction("粘贴为新行")
        delete_action = menu.addAction("删除")

        menu.addSeparator()

        # 创建 "导出" 的级联菜单
        export_menu = menu.addMenu("导出")
        export_table_action = export_menu.addAction("导出表格")

        # 刷新
        refresh_action = menu.addAction("刷新")

        # 检查是否有选中的单元格或行
        selected_items = self.table.selectedItems()
        has_selection = len(selected_items) > 0

        # 根据选择状态启用/禁用相关操作
        if not has_selection:
            copy_action.setEnabled(False)
            cut_action.setEnabled(False)
            delete_action.setEnabled(False)
        else:
            copy_action.setEnabled(True)
            cut_action.setEnabled(True)
            delete_action.setEnabled(True)

        # 检查是否有复制的数据
        has_copied_data = hasattr(self, 'table_selection_handler') and \
                         hasattr(self.table_selection_handler, 'clipboard_data') and \
                         len(self.table_selection_handler.clipboard_data) > 0

        if not has_copied_data:
            paste_action.setEnabled(False)
            paste_as_new_action.setEnabled(False)
        else:
            paste_action.setEnabled(True)
            paste_as_new_action.setEnabled(True)

        # 显示菜单并获取所选操作
        action = menu.exec_(self.table.mapToGlobal(position))

        # 处理菜单操作
        if action == add_blank_row_action:
            self.add_record()  # 调用正确的add_record方法，会在数据库中创建记录并生成ID
        elif action == insert_above_action:
            # 使用行插入管理器进行多行插入
            self.insert_rows_above()
        elif action == insert_below_action:
            # 使用行插入管理器进行多行插入
            self.insert_rows_below()
        elif action == copy_action:
            self._copy_selection()
        elif action == cut_action:
            self._cut_selection()
        elif action == paste_action:
            self._paste_selection()
        elif action == paste_as_new_action:
            self._paste_as_new_row()
        elif action == delete_action:
            self.delete_selected_records()
        elif action == export_table_action:
            self._export_data()
        elif action == refresh_action:
            self.load_data()

    def insert_rows_above(self):
        """向上方插入行"""
        if hasattr(self, 'row_insert_manager'):
            self.row_insert_manager.insert_rows_above()
        else:
            self.logger.error("行插入管理器未初始化")
            QMessageBox.warning(self, "错误", "行插入功能未就绪")

    def insert_rows_below(self):
        """向下方插入行"""
        if hasattr(self, 'row_insert_manager'):
            self.row_insert_manager.insert_rows_below()
        else:
            self.logger.error("行插入管理器未初始化")
            QMessageBox.warning(self, "错误", "行插入功能未就绪")

    def _insert_row_above(self):
        """向上方插入行 - 实际调用add_record创建新记录"""
        self.add_record()

    def _insert_row_below(self):
        """向下方插入行 - 实际调用add_record创建新记录"""
        self.add_record()

    def _copy_selection(self):
        """复制选中内容"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.copy_selected_cells()
        else:
            self.logger.warning("表格选择处理器未初始化")

    
    def _cut_selection(self):
        """剪切选中内容"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.cut_selected_cells()
        else:
            self.logger.warning("表格选择处理器未初始化")

    def _paste_selection(self):
        """粘贴内容"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.paste_to_selected_cells()
        else:
            self.logger.warning("表格选择处理器未初始化")

    def _paste_as_new_row(self):
        """粘贴为新行"""
        # 这个功能需要根据具体需求实现
        QMessageBox.information(self, "提示", "粘贴为新行功能待实现")
        self.logger.info("粘贴为新行功能被调用")

    def _export_data(self):
        """导出数据"""
        QMessageBox.information(self, "提示", "导出功能待实现")
        self.logger.info("导出功能被调用")
    
    def get_selected_records(self):
        """获取选中的记录ID列表"""
        selected_ids = []
        selected_rows = set()

        # 🔧 关键修复：使用selectionModel获取选中行，支持包含QComboBox的行选择
        selection_model = self.table.selectionModel()
        if selection_model:
            selected_indexes = selection_model.selectedRows()
            for index in selected_indexes:
                selected_rows.add(index.row())

        # 如果没有通过selectionModel获取到选中行，回退到原有方法
        if not selected_rows:
            for item in self.table.selectedItems():
                selected_rows.add(item.row())

        for row in selected_rows:
            id_item = self.table.item(row, 0)
            if id_item:
                try:
                    record_id = int(id_item.text())
                    selected_ids.append(record_id)
                except ValueError:
                    pass

        return selected_ids
    
    def _on_cell_changed(self, row, column):
        """单元格内容变更处理"""
        try:
            # 防止在更新显示时触发数据保存
            if hasattr(self, '_updating_display') and self._updating_display:
                return

            # 🔧 新增：首先调用父类的撤销重做功能
            super()._on_cell_changed(row, column)

            # 获取变更的字段名和值
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 如果是公式列，不允许直接编辑，跳过保存
            if hasattr(self, 'formula_columns') and field_name in self.formula_columns:
                self.logger.debug(f"跳过公式列 {field_name} 的数据保存")
                return

            # 获取记录ID
            id_item = self.table.item(row, 0)
            if not id_item:
                return

            record_id_text = id_item.text()
            if not record_id_text or not record_id_text.isdigit():
                return

            record_id = int(record_id_text)

            new_value = self.table.item(row, column).text()

            # 更新数据库
            update_data = {field_name: new_value}

            if self.change_model.update_change_record(record_id, update_data, field_groups=self.field_groups):
                self.record_updated.emit(record_id)
                self.logger.debug(f"更新记录 {record_id} 的字段 {field_name} 为 {new_value}")
            else:
                # 更新失败，恢复原值
                self.load_data()
                QMessageBox.warning(self, "警告", "更新记录失败")

        except Exception as e:
            self.logger.error(f"更新零部件换装记录失败: {e}")
            self.load_data()  # 恢复数据

    def _on_combo_text_changed(self, row, column, text):
        """处理QComboBox文本变更事件"""
        try:
            # 获取记录ID
            id_item = self.table.item(row, 0)
            if not id_item:
                return

            record_id_text = id_item.text()
            if not record_id_text or not record_id_text.isdigit():
                return

            record_id = int(record_id_text)

            # 获取变更的字段名
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 获取字段定义进行验证
            field_def = self.field_definitions.get(field_name, {})
            if field_def.get("type") == "QComboBox":
                # 验证输入是否在选项中
                valid_options = field_def.get("options", [])
                if text and text not in valid_options:
                    QMessageBox.warning(self, "输入错误", f"请输入有效的选项值")
                    # 恢复原值
                    combo = self.table.cellWidget(row, column)
                    if combo:
                        combo.setCurrentText("")
                    return

            # 更新数据库
            update_data = {field_name: text}

            if self.change_model.update_change_record(record_id, update_data, field_groups=self.field_groups):
                self.record_updated.emit(record_id)
                self.logger.debug(f"更新记录 {record_id} 的字段 {field_name} 为 {text}")

                # 🔧 改进：先同步单元格数据到数据框，然后触发公式重新计算
                self._sync_single_cell_to_dataframe(row, column, text)

                # 🔧 新增：触发公式重新计算
                if (hasattr(self, 'formula_enabled') and self.formula_enabled and
                    hasattr(self, 'formula_columns') and self.formula_columns):
                    self._trigger_formula_recalculation(field_name, row)

            else:
                # 更新失败，恢复原值
                self.load_data()
                QMessageBox.warning(self, "警告", "更新记录失败")

        except Exception as e:
            self.logger.error(f"更新零部件换装记录失败: {e}")
            self.load_data()  # 恢复数据

    def _on_selection_changed(self):
        """选择变更处理"""
        selected_ids = self.get_selected_records()
        if selected_ids:
            self.record_selected.emit(selected_ids[0])
    
    def _load_field_groups(self):
        """加载字段分组配置"""
        try:
            # 定义默认字段分组
            default_groups = {
                "单独列字段": ["id", "样车编号", "换装零部件", "换装原因及主要考察点", "换装后情况", "换装时间", "修改时间"],
                "固定字段": ["换装里程"],
                "可变字段": ["备注"]
            }

            # 尝试从新的配置文件路径加载（字段导入功能生成的文件）
            config_path = os.path.join("config", "change_field_groups.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            # 兼容旧的配置文件路径
            old_config_path = os.path.join("config", "零部件换装表字段分组.json")
            if os.path.exists(old_config_path):
                with open(old_config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            return default_groups
        except Exception as e:
            self.logger.error(f"加载字段分组配置失败: {e}")
            return {
                "单独列字段": ["id", "样车编号", "换装零部件", "换装原因及主要考察点", "换装后情况", "换装时间", "修改时间"],
                "固定字段": ["换装里程"],
                "可变字段": ["备注"]
            }
    
    def _on_column_resized(self, logical_index, old_size, new_size):
        """列宽变化时保存表格状态"""
        try:
            self.table_state_manager.save_table_state(self.table)
        except Exception as e:
            self.logger.error(f"保存列宽状态失败: {e}")

    def _on_row_resized(self, logical_index, old_size, new_size):
        """行高变化时保存表格状态"""
        try:
            self.table_state_manager.save_table_state(self.table)
        except Exception as e:
            self.logger.error(f"保存行高状态失败: {e}")

    def _reorder_columns(self, df):
        """重新排列列顺序，将修改时间字段放在备注字段之后"""
        try:
            columns = df.columns.tolist()

            # 如果修改时间和备注字段都存在，则调整顺序
            if '修改时间' in columns and '备注' in columns:
                # 移除修改时间字段
                columns.remove('修改时间')

                # 找到备注字段的位置
                remark_index = columns.index('备注')

                # 在备注字段之后插入修改时间字段
                columns.insert(remark_index + 1, '修改时间')

                # 重新排列DataFrame的列
                df = df[columns]

                self.logger.info("已将修改时间字段移动到备注字段之后")

            return df

        except Exception as e:
            self.logger.error(f"重新排列列顺序失败: {e}")
            return df

    def _load_field_definitions(self):
        """加载字段定义配置"""
        try:
            # 加载字段定义配置文件
            config_path = os.path.join(os.getcwd(), 'config', 'change_table_field_definitions.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    definitions = json.load(f)
                    self.logger.info(f"成功加载零部件换装表字段定义配置: {config_path}")
                    return definitions
            else:
                self.logger.warning(f"零部件换装表字段定义配置文件不存在: {config_path}")

        except Exception as e:
            self.logger.error(f"加载零部件换装表字段定义配置失败: {e}")

        return {}

    def reload_field_definitions(self):
        """重新加载字段定义配置"""
        try:
            self.field_definitions = self._load_field_definitions()
            self.logger.info("字段定义配置重新加载成功")
        except Exception as e:
            self.logger.error(f"重新加载字段定义配置失败: {e}")

    def import_field_json(self, json_data):
        """导入字段JSON配置"""
        try:
            # 重新加载字段分组配置
            self.field_groups = self._load_field_groups()

            # 重新加载字段定义配置
            self.reload_field_definitions()

            # 重新加载数据以应用新的字段配置
            self.load_data()
            self.logger.info("零部件换装表字段JSON配置导入成功")
        except Exception as e:
            self.logger.error(f"导入零部件换装表字段JSON配置失败: {e}")
            raise

    # ==================== 公式功能相关方法 ====================

    def load_formula_config(self):
        """加载公式配置"""
        try:
            if self.formula_config_manager.load_table_formulas(self.table_name, self.formula_engine):
                # 更新公式列标识
                column_formulas = self.formula_engine.get_column_formulas(self.table_name)
                self.formula_columns = set(column_formulas.keys())

                # 更新列头样式
                for column_name in self.formula_columns:
                    self._update_column_header_style(column_name, True)

                self.logger.info(f'成功加载零部件换装表公式配置，共{len(self.formula_columns)}个公式列')
            else:
                self.logger.warning(f'加载零部件换装表公式配置失败')

        except Exception as e:
            self.logger.error(f'加载零部件换装表公式配置异常: {e}')

    def _save_formula_results_to_database(self, formula_column: str, calculated_series: pd.Series):
        """将公式计算结果保存到数据库 - 优化版本，使用批量更新"""
        try:
            # 🔧 性能优化：收集所有需要更新的数据，进行批量更新
            batch_updates = []

            for row_index, value in calculated_series.items():
                record_id = self._get_record_id_for_row(row_index)
                if record_id:
                    batch_updates.append({
                        'id': record_id,
                        'data': {formula_column: value}
                    })

            if batch_updates:
                # 🔧 使用批量更新方法（如果数据模型支持）
                if hasattr(self.change_model, 'batch_update_change_records'):
                    success_count = self.change_model.batch_update_change_records(batch_updates, field_groups=self.field_groups)
                    self.logger.info(f"批量保存公式结果到数据库: {formula_column}列，成功更新{success_count}/{len(batch_updates)}条记录")
                else:
                    # 🔧 降级到逐条更新，但减少日志输出
                    success_count = 0
                    for update_item in batch_updates:
                        success = self.change_model.update_change_record(update_item['id'], update_item['data'], field_groups=self.field_groups)
                        if success:
                            success_count += 1

                    # 🔧 只输出汇总日志，避免大量DEBUG日志
                    self.logger.info(f"保存公式结果到数据库: {formula_column}列，成功更新{success_count}/{len(batch_updates)}条记录")

                    # 🔧 如果有失败的记录，输出警告
                    if success_count < len(batch_updates):
                        self.logger.warning(f"部分公式结果保存失败: {formula_column}列，失败{len(batch_updates) - success_count}条记录")

        except Exception as e:
            self.logger.error(f"保存零部件换装记录公式结果到数据库失败: {e}")

    def _get_record_id_for_row(self, row_index: int):
        """获取指定行的记录ID"""
        try:
            # 零部件换装表的ID在第一列
            id_item = self.table.item(row_index, 0)
            if id_item and id_item.text().isdigit():
                return int(id_item.text())
            return None
        except Exception as e:
            self.logger.error(f"获取零部件换装记录行{row_index}的ID失败: {e}")
            return None

    def save_formula_config(self):
        """保存公式配置"""
        try:
            if self.formula_config_manager.save_table_formulas(self.table_name, self.formula_engine):
                self.logger.info(f'成功保存零部件换装表公式配置')
                return True
            else:
                self.logger.error(f'保存零部件换装表公式配置失败')
                return False

        except Exception as e:
            self.logger.error(f'保存零部件换装表公式配置异常: {e}')
            return False

    def load_data(self):
        """
        加载数据并应用公式计算
        重写基类方法以支持公式功能
        """
        try:
            # 获取数据 - 使用change_model
            if hasattr(self, 'change_model') and hasattr(self.change_model, 'get_all_records'):
                df = self.change_model.get_all_records()
            else:
                df = pd.DataFrame()  # 如果没有数据模型，使用空数据框

            if df.empty:
                self.logger.info('没有零部件换装表数据')
                self.data_frame = pd.DataFrame()
                # 清空表格显示
                self.table.setRowCount(0)
                self.table.setColumnCount(0)
                return

            # 先展开JSON字段，确保公式计算能访问到所有字段
            expanded_df = self._expand_json_fields(df)

            # 设置数据框
            self.data_frame = expanded_df.copy()

            # 如果有公式列，重新计算
            if hasattr(self, 'formula_enabled') and self.formula_enabled and hasattr(self, 'formula_columns') and self.formula_columns:
                if hasattr(self, 'formula_engine'):
                    self.data_frame = self.formula_engine.calculate_all_formula_columns(
                        self.table_name, self.data_frame
                    )

            # 设置表格显示 - 直接使用已处理的数据框
            self._setup_table_display_only(self.data_frame)

            # 加载公式配置
            self.load_formula_config()

            self.logger.info(f'成功加载 {len(df)} 条零部件换装表记录')

        except Exception as e:
            self.logger.error(f'加载零部件换装表数据失败: {e}')

    def _setup_table_with_data(self, df):
        """设置表格数据显示"""
        try:
            # 展开JSON字段
            df = self._expand_json_fields(df)

            # 调用显示方法
            self._setup_table_display_only(df)

        except Exception as e:
            self.logger.error(f"设置零部件换装表显示失败: {e}")

    def _setup_table_display_only(self, df):
        """仅设置表格显示，不进行JSON字段展开"""
        try:
            # 根据显示设置获取可见字段和顺序
            visible_fields = self.display_settings.get("visible_fields", [])
            field_order = self.display_settings.get("field_order", [])

            # 使用字段顺序，只显示可见字段且存在于数据框中的字段
            ordered_visible_fields = [field for field in field_order
                                    if field in visible_fields and field in df.columns]

            # 如果没有有效的字段顺序，使用原始列顺序
            if not ordered_visible_fields:
                ordered_visible_fields = df.columns.tolist()

            # 重新排列数据框列顺序
            df = df[ordered_visible_fields]

            # 设置表格行列数
            self.table.setRowCount(len(df))
            self.table.setColumnCount(len(df.columns))

            # 设置表头 - 使用用户配置的字段顺序，不再强制调整列顺序
            self.table.setHorizontalHeaderLabels(df.columns.tolist())

            # 填充数据
            for row in range(len(df)):
                for col in range(len(df.columns)):
                    field_name = df.columns[col]
                    value = df.iloc[row, col]
                    if value is None:
                        value = ""

                    # 使用字段定义创建控件
                    widget = self._create_cell_widget(field_name, value, row, col)

                    if widget is not None:
                        # 使用特殊控件
                        self.table.setCellWidget(row, col, widget)
                    else:
                        # 使用默认的文本单元格
                        item = QTableWidgetItem(str(value))
                        self.table.setItem(row, col, item)

            self.logger.info(f"零部件换装表显示设置完成，共 {len(df)} 行 {len(df.columns)} 列")

            # 🔧 **关键修复**：零部件换装表显示设置完成后重新安装QComboBox事件过滤器
            # 这确保新创建的QComboBox控件能够正确处理多选事件
            if hasattr(self, 'table_selection_handler') and self.table_selection_handler:
                self.table_selection_handler.force_reinstall_all_combobox_filters()
                self.logger.info(f"🔧 零部件换装表显示完成，已重新安装QComboBox事件过滤器")

        except Exception as e:
            self.logger.error(f"设置零部件换装表显示失败: {e}")



    def delete_record(self):
        """删除记录后重新计算公式"""
        try:
            # 调用原有的删除逻辑（如果存在）
            # 然后重新加载数据以触发公式计算
            self.load_data()

            self.logger.info(f'零部件换装表记录删除完成')

        except Exception as e:
            self.logger.error(f'零部件换装表删除记录失败: {e}')

    def _load_display_settings(self):
        """加载显示设置"""
        try:
            from src.managers.change_table_manager import ChangeTableManager
            manager = ChangeTableManager()
            return manager.get_display_settings()
        except Exception as e:
            self.logger.error(f"加载零部件换装表显示设置失败: {e}")
            return {"visible_fields": [], "field_order": []}

    def apply_display_settings(self, settings):
        """应用显示设置"""
        try:
            self.display_settings = settings
            # 重新加载数据以应用新的显示设置
            self.load_data()
            self.logger.info("零部件换装表显示设置已应用")
        except Exception as e:
            self.logger.error(f"应用零部件换装表显示设置失败: {e}")
