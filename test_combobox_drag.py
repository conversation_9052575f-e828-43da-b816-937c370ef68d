#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试QComboBox拖拽多选功能的简单脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from src.views.base_table_widget import BaseTableWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("QComboBox拖拽多选功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
测试说明：
1. 普通拖拽：在QComboBox字段上按住鼠标左键拖拽，应该能选择多个单元格
2. Ctrl+点击：按住Ctrl键点击QComboBox字段，应该能添加/取消选择
3. Shift+点击：按住Shift键点击QComboBox字段，应该能范围选择
4. 对比QLineEdit字段的行为，确保一致性

请在下面的表格中测试这些功能：
        """)
        layout.addWidget(info_label)
        
        # 创建测试表格
        self.table_widget = TestTableWidget()
        layout.addWidget(self.table_widget)
        
        # 初始化测试数据
        self.init_test_data()
    
    def init_test_data(self):
        """初始化测试数据"""
        # 添加一些测试行
        for i in range(5):
            self.table_widget.add_record(batch_mode=True)

class TestTableWidget(BaseTableWidget):
    def __init__(self):
        super().__init__()
        self.table_name = "测试表格"
        self.logger_name = "test_table"
        
        # 定义字段配置，包含QComboBox和QLineEdit字段
        self.field_configs = [
            {"name": "序号", "type": "QLineEdit", "width": 80},
            {"name": "QComboBox字段1", "type": "QComboBox", "width": 150, "options": ["选项1", "选项2", "选项3", "选项4"]},
            {"name": "QLineEdit字段1", "type": "QLineEdit", "width": 150},
            {"name": "QComboBox字段2", "type": "QComboBox", "width": 150, "options": ["A", "B", "C", "D", "E"]},
            {"name": "QLineEdit字段2", "type": "QLineEdit", "width": 150},
            {"name": "QComboBox字段3", "type": "QComboBox", "width": 150, "options": ["测试1", "测试2", "测试3"]},
        ]
        
        self.init_table()
    
    def get_default_record(self):
        """获取默认记录"""
        return {
            "序号": "",
            "QComboBox字段1": "",
            "QLineEdit字段1": "",
            "QComboBox字段2": "",
            "QLineEdit字段2": "",
            "QComboBox字段3": "",
        }

def main():
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("QComboBox拖拽多选功能测试启动")
    print("请在表格中测试以下功能：")
    print("1. 普通拖拽选择（无修饰键）")
    print("2. Ctrl+点击多选")
    print("3. Shift+点击范围选择")
    print("4. 对比QComboBox和QLineEdit字段的行为")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
